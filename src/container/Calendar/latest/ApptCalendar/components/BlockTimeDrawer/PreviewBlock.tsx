import { memo, useMemo } from 'react';
import { isBlockDrawerEditPreset } from '../../../../../../store/calendarLatest/calendar.types';
import { CalendarCardTypeMixed, type MoeEventInfo } from '../../../../../../store/calendarLatest/card.types';
import { getCardUUID, getDefaultEventInfo } from '../../../../../../store/calendarLatest/card.utils';
import { usePreviewCards } from '../../hooks/usePreviewCards';
import { useBlockTimeConfig } from './hooks/useBlockTimeConfig';

export const PreviewBlock = memo(function PreviewBlock() {
  const [{ visibleAddBlockDrawer, preset }] = useBlockTimeConfig();
  const blockTimeCards = useMemo(() => {
    const isEdit = isBlockDrawerEditPreset(preset);
    if (!visibleAddBlockDrawer || isEdit) {
      return [];
    }
    const { staffId, blockStartDate: blockTimeDate, duration } = preset;
    const cardId = getCardUUID(0, CalendarCardTypeMixed.NewBlock);
    const card: MoeEventInfo = getDefaultEventInfo({
      cardId,
      className: 'moe-disable-event-resize',
      start: blockTimeDate.toDate(),
      end: blockTimeDate.add(duration, 'minute').toDate(),
      staffId,
    });
    return [card];
  }, [visibleAddBlockDrawer, preset]);

  usePreviewCards(blockTimeCards);

  return null;
});
